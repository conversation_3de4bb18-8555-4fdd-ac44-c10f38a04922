# 🆓 Free LLM API Setup Guide

This guide will help you set up **completely free** LLM providers to replace OpenAI's paid API in your SOP Builder MVP system.

## 🎯 Overview

We've implemented a **multi-provider LLM system** that automatically tries free APIs in this order:
1. **🚀 Groq** - Ultra-fast, free tier, no explicit limits
2. **🤗 Hugging Face** - 300 requests/hour free for registered users
3. **💰 Together AI** - $25 free credits for new accounts
4. **🌟 OpenRouter** - Free DeepSeek V3 access, multiple free models

## 📋 Setup Instructions

### 1. 🚀 Groq (Primary - RECOMMENDED)

**Why Groq?** Ultra-fast inference, free tier, OpenAI-compatible API

1. **Sign up**: Go to https://console.groq.com/
2. **Create account**: Use your email to register
3. **Get API key**:
   - Go to "API Keys" section
   - Click "Create API Key"
   - Copy the key (starts with `gsk_...`)
4. **Add to .env**:
   ```bash
   GROQ_API_KEY=gsk_your_actual_groq_api_key_here
   ```

**Models available:**
- `llama-3.1-70b-versatile` (recommended for quality)
- `llama-3.1-8b-instant` (faster, good quality)
- `mixtral-8x7b-32768` (alternative option)

### 2. 🤗 Hugging Face (Secondary)

**Why Hugging Face?** 300 free requests/hour, many models available

1. **Sign up**: Go to https://huggingface.co/join
2. **Create account**: Register with your email
3. **Get token**:
   - Go to https://huggingface.co/settings/tokens
   - Click "New token"
   - Choose "Read" access
   - Copy the token (starts with `hf_...`)
4. **Add to .env**:
   ```bash
   HUGGINGFACE_API_TOKEN=hf_your_actual_huggingface_token_here
   ```

### 3. 💰 Together AI (Tertiary)

**Why Together AI?** $25 free credits for new accounts (goes far!)

1. **Sign up**: Go to https://api.together.ai/
2. **Create account**: Register with your email
3. **Get API key**:
   - Go to "API Keys" section
   - Create new API key
   - Copy the key
4. **Add to .env**:
   ```bash
   TOGETHER_API_KEY=your_actual_together_api_key_here
   ```

### 4. 🌟 OpenRouter (Quaternary - CONFIGURED!)

**Why OpenRouter?** Free access to DeepSeek V3 and other models

✅ **Already configured with your API key!**
- Your key: `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`
- Model: `deepseek/deepseek-chat` (free tier)
- Ready to use immediately!

**Other free models available:**
- `google/gemma-2-9b-it:free`
- `meta-llama/llama-3.1-8b-instruct:free`
- `microsoft/phi-3-mini-128k-instruct:free`

## ⚙️ Configuration

### Update Your .env File

Open `sop-templates/.env` and update these values:

```bash
# 🚀 PRIMARY: Groq (Ultra-fast, free tier)
GROQ_API_KEY=gsk_your_actual_groq_api_key_here
GROQ_MODEL=llama-3.1-70b-versatile

# 🤗 SECONDARY: Hugging Face (300 requests/hour free)
HUGGINGFACE_API_TOKEN=hf_your_actual_huggingface_token_here

# 💰 TERTIARY: Together AI ($25 free credits)
TOGETHER_API_KEY=your_actual_together_api_key_here

# 🔧 LLM CONFIGURATION
LLM_PROVIDER=auto  # Will try all providers in order
```

### Provider Priority

The system will automatically try providers in this order:
1. **Groq** (if configured)
2. **Hugging Face** (if Groq fails)
3. **Together AI** (if both above fail)
4. **Fallback content** (if all fail)

## 🧪 Testing Your Setup

### Test All Providers

```bash
# Activate virtual environment
.\venv\Scripts\activate

# Test the LLM providers
python -c "
from scripts.utils.llm_client import FreeLLMClient
client = FreeLLMClient()
print('Available providers:', client.get_available_providers())
print('Testing providers:', client.test_providers())
"
```

### Generate SOP with Free LLMs

```bash
# Generate restaurant SOP using free LLMs
python scripts/generators/sop_generator.py --type restaurant --verbose --no-cache
```

## 📊 Expected Results

### ✅ Success Indicators

When working correctly, you should see:
```
✅ LLM client initialized with providers: ['groq', 'huggingface', 'together']
🚀 Trying provider: groq
✅ Success with groq
✅ Content generated using groq (llama-3.1-70b-versatile)
```

### 🔄 Automatic Fallback

If one provider fails:
```
🚀 Trying provider: groq
❌ groq failed: Rate limit exceeded
🤗 Trying provider: huggingface
✅ Success with huggingface
```

## 💡 Tips for Maximum Free Usage

### 1. Optimize API Calls
- **Use caching**: Generated content is cached for 24 hours
- **Batch generation**: Generate multiple sections at once
- **Smart retries**: Built-in exponential backoff

### 2. Provider-Specific Tips

**Groq:**
- No explicit rate limits on free tier
- Very fast responses (usually < 2 seconds)
- Best for development and testing

**Hugging Face:**
- 300 requests/hour limit
- Resets every hour
- Good for moderate usage

**Together AI:**
- $25 credit goes far (thousands of requests)
- Pay-per-use after credits
- Best for production-like testing

### 3. Monitoring Usage

Check your usage:
- **Groq**: https://console.groq.com/usage
- **Hugging Face**: No usage dashboard for free tier
- **Together AI**: https://api.together.ai/usage

## 🚨 Troubleshooting

### Common Issues

1. **"No LLM providers configured"**
   - Check your API keys in `.env`
   - Ensure keys don't contain `your_*_key_here`

2. **"401 Unauthorized"**
   - Verify API key is correct
   - Check if account is active

3. **"Rate limit exceeded"**
   - System will automatically try next provider
   - Wait for rate limit reset

4. **Import errors**
   - Ensure `requests` is installed: `pip install requests`
   - Check Python path in scripts

### Debug Mode

Enable detailed logging:
```bash
# Set debug mode in .env
DEBUG=True

# Run with verbose output
python scripts/generators/sop_generator.py --type restaurant --verbose
```

## 🎉 Success!

Once configured, your SOP Builder will:
- ✅ Generate high-quality content using free LLMs
- ✅ Automatically fallback between providers
- ✅ Cache content to minimize API calls
- ✅ Work without any paid subscriptions

**Next Steps:**
1. Set up at least one provider (Groq recommended)
2. Test the system with `--verbose` flag
3. Generate your first free AI-powered SOP template!

---

**Need help?** The system includes comprehensive fallback content, so it will work even if no providers are configured. But with free LLMs, you'll get much better, customized content!
