# =============================================================================
# SOP BUILDER MVP - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual API keys and configuration

# =============================================================================
# FREE LLM API CONFIGURATION
# =============================================================================
# Multiple free LLM providers with automatic fallback
# Priority order: Groq -> Hugging Face -> Together AI -> Local fallback

# 🚀 PRIMARY: Groq (Ultra-fast, free tier, OpenAI-compatible)
# Sign up at: https://console.groq.com/
# Free tier: No explicit limits, very fast inference
# Models: llama-3.1-70b-versatile, llama-3.1-8b-instant, mixtral-8x7b-32768
GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama-3.1-70b-versatile
GROQ_BASE_URL=https://api.groq.com/openai/v1

# 🤗 SECONDARY: Hugging Face (300 requests/hour free)
# Get token at: https://huggingface.co/settings/tokens
# Free tier: 300 requests/hour for registered users
# Models: Many open-source models available
HUGGINGFACE_API_TOKEN=your_huggingface_token_here
HUGGINGFACE_MODEL=microsoft/DialoGPT-large
HUGGINGFACE_BASE_URL=https://api-inference.huggingface.co/models

# 💰 TERTIARY: Together AI ($25 free credits for new accounts)
# Sign up at: https://api.together.ai/
# Free credits: $25 for new accounts (goes far with efficient usage)
# Models: meta-llama/Llama-3-70b-chat-hf, mistralai/Mixtral-8x7B-Instruct-v0.1
TOGETHER_API_KEY=your_together_api_key_here
TOGETHER_MODEL=meta-llama/Llama-3-70b-chat-hf
TOGETHER_BASE_URL=https://api.together.xyz/v1

# 🌟 QUATERNARY: OpenRouter (Free DeepSeek V3 access)
# Sign up at: https://openrouter.ai/
# Free tier: DeepSeek V3 and other free models available
# Models: deepseek/deepseek-chat, google/gemma-2-9b-it, meta-llama/llama-3.1-8b-instruct:free
OPENROUTER_API_KEY=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
OPENROUTER_MODEL=deepseek/deepseek-chat
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# 🔧 LLM CONFIGURATION
LLM_PROVIDER=auto  # Options: groq, huggingface, together, openrouter, auto (tries all in order)
LLM_MAX_TOKENS=2000
LLM_TEMPERATURE=0.7
LLM_TIMEOUT=30
LLM_RETRY_ATTEMPTS=3

# 📚 LEGACY: OpenAI (for reference - not used with free providers)
# Keep for potential future paid usage
OPENAI_API_KEY=your_openai_key_here
OPENAI_MODEL=gpt-4

# 🧠 ANTHROPIC: Claude (for reference - not free)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# DATA MANAGEMENT & STORAGE
# =============================================================================

# Google Sheets for compliance data management
# Create service account at: https://console.cloud.google.com/
# Download JSON credentials and place in ./config/google_credentials.json
GOOGLE_SHEETS_CREDENTIALS_PATH=./config/google_credentials.json

# Google Sheet ID containing compliance requirements and industry data
# Extract from sheet URL: https://docs.google.com/spreadsheets/d/{SHEET_ID}/edit
COMPLIANCE_SHEET_ID=your_google_sheet_id_here

# =============================================================================
# DESIGN & BRANDING
# =============================================================================

# Canva API for automated design generation
# Get API access from: https://www.canva.com/developers/
# Used for: Template covers, branded elements, marketing materials
CANVA_API_KEY=your_canva_api_key_here

# Your Canva Brand Kit ID for consistent branding
# Find in Canva Brand Kit settings
CANVA_BRAND_KIT_ID=your_brand_kit_id_here

# =============================================================================
# SALES & DISTRIBUTION
# =============================================================================

# Gumroad API for automated product distribution
# Get access token from: https://gumroad.com/settings/advanced
# Used for: Uploading products, managing sales, customer delivery
GUMROAD_ACCESS_TOKEN=your_gumroad_token_here

# Gumroad Product IDs for each template type (JSON format)
# Create products first, then add their IDs here
GUMROAD_PRODUCT_IDS={"restaurant": "prod_id_1", "healthcare": "prod_id_2"}

# =============================================================================
# EMAIL & MARKETING
# =============================================================================

# Mailchimp API for customer communication
# Get API key from: https://mailchimp.com/help/about-api-keys/
# Used for: Customer notifications, marketing campaigns, updates
MAILCHIMP_API_KEY=your_mailchimp_key_here

# Mailchimp Audience/List ID for SOP customers
# Find in Audience settings
MAILCHIMP_LIST_ID=your_list_id_here

# =============================================================================
# VIDEO GENERATION (OPTIONAL)
# =============================================================================

# Synthesia API for AI video generation
# Get API access from: https://www.synthesia.io/
# Used for: Creating training videos from SOP content
SYNTHESIA_API_KEY=your_synthesia_key_here

# HeyGen API for alternative video generation
# Get API access from: https://heygen.com/
# Used as backup for video creation
HEYGEN_API_KEY=your_heygen_key_here

# =============================================================================
# MONITORING & ALERTS
# =============================================================================

# Slack webhook for system notifications
# Create webhook at: https://api.slack.com/messaging/webhooks
# Used for: Error alerts, generation completion, daily reports
SLACK_WEBHOOK_URL=your_slack_webhook_here

# Email address for error notifications
# Used when Slack is unavailable or for critical alerts
ERROR_EMAIL=<EMAIL>

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment mode (development/staging/production)
ENVIRONMENT=development

# Enable debug logging and verbose output
DEBUG=True

# Local file storage path for generated content
LOCAL_STORAGE_PATH=./outputs

# Maximum API retries for failed requests
MAX_API_RETRIES=3

# Cache duration for generated content (in hours)
CACHE_DURATION_HOURS=24
