{"metadata": {"type": "restaurant", "version": "1.0", "generated_date": "2025-05-29T07:25:51.946711", "compliance_standards": ["FDA Food Code 2022", "HACCP Principles", "ServSafe Guidelines", "Local Health Department Requirements"], "industry_data": {}, "generation_method": "ai_generated"}, "sections": {"introduction": {"content": "\n# introduction\n\n## Overview\nThis section covers the introduction requirements for restaurant operations.\n\n## Key Points\n- Follow established procedures\n- Maintain quality standards\n- Ensure compliance with regulations\n- Document all activities\n\n## Next Steps\n- Review detailed procedures\n- Complete required training\n- Implement quality controls\n- Monitor compliance\n\n*Note: This is fallback content. Please review and customize based on specific requirements.*\n        ", "order": 1, "required": true, "generated_at": "2025-05-29T07:26:00.931681", "cached": false}, "daily_procedures": {"content": "\n# daily_procedures\n\n## Overview\nThis section covers the daily_procedures requirements for restaurant operations.\n\n## Key Points\n- Follow established procedures\n- Maintain quality standards\n- Ensure compliance with regulations\n- Document all activities\n\n## Next Steps\n- Review detailed procedures\n- Complete required training\n- Implement quality controls\n- Monitor compliance\n\n*Note: This is fallback content. Please review and customize based on specific requirements.*\n        ", "order": 2, "required": true, "generated_at": "2025-05-29T07:26:08.421817", "cached": false}, "food_storage": {"content": "\n# food_storage\n\n## Overview\nThis section covers the food_storage requirements for restaurant operations.\n\n## Key Points\n- Follow established procedures\n- Maintain quality standards\n- Ensure compliance with regulations\n- Document all activities\n\n## Next Steps\n- Review detailed procedures\n- Complete required training\n- Implement quality controls\n- Monitor compliance\n\n*Note: This is fallback content. Please review and customize based on specific requirements.*\n        ", "order": 3, "required": true, "generated_at": "2025-05-29T07:26:15.795690", "cached": false}, "cleaning_sanitization": {"content": "\n# cleaning_sanitization\n\n## Overview\nThis section covers the cleaning_sanitization requirements for restaurant operations.\n\n## Key Points\n- Follow established procedures\n- Maintain quality standards\n- Ensure compliance with regulations\n- Document all activities\n\n## Next Steps\n- Review detailed procedures\n- Complete required training\n- Implement quality controls\n- Monitor compliance\n\n*Note: This is fallback content. Please review and customize based on specific requirements.*\n        ", "order": 4, "required": true, "generated_at": "2025-05-29T07:26:23.221051", "cached": false}, "crisis_response": {"content": "\n# crisis_response\n\n## Overview\nThis section covers the crisis_response requirements for restaurant operations.\n\n## Key Points\n- Follow established procedures\n- Maintain quality standards\n- Ensure compliance with regulations\n- Document all activities\n\n## Next Steps\n- Review detailed procedures\n- Complete required training\n- Implement quality controls\n- Monitor compliance\n\n*Note: This is fallback content. Please review and customize based on specific requirements.*\n        ", "order": 5, "required": true, "generated_at": "2025-05-29T07:26:32.365635", "cached": false}, "employee_training": {"content": "\n# employee_training\n\n## Overview\nThis section covers the employee_training requirements for restaurant operations.\n\n## Key Points\n- Follow established procedures\n- Maintain quality standards\n- Ensure compliance with regulations\n- Document all activities\n\n## Next Steps\n- Review detailed procedures\n- Complete required training\n- Implement quality controls\n- Monitor compliance\n\n*Note: This is fallback content. Please review and customize based on specific requirements.*\n        ", "order": 6, "required": true, "generated_at": "2025-05-29T07:26:39.759378", "cached": false}}, "generation_stats": {"total_sections": 6, "successful_sections": 6, "failed_sections": 0, "cached_sections": 0, "generation_time_seconds": 47.813724}, "compliance_features": {"audit_trail": {"enabled": true, "fields": ["user", "timestamp", "action", "section"]}, "version_control": {"enabled": true, "auto_increment": true}, "regulatory_links": {"FDA": "https://www.fda.gov/food/fda-food-code/food-code-2022", "USDA": "https://www.fsis.usda.gov/food-safety", "ServSafe": "https://www.servsafe.com/access/ss/catalog/productdetail/ssfsmv7"}, "update_notifications": {"enabled": true, "frequency": "monthly"}}, "interactive_elements": [{"type": "qr_code", "data": "https://www.fda.gov/food/fda-food-code/food-code-2022", "label": "Scan for latest FDA requirements"}, {"type": "qr_code", "data": "https://www.fsis.usda.gov/food-safety", "label": "Scan for latest USDA requirements"}, {"type": "qr_code", "data": "https://www.servsafe.com/access/ss/catalog/productdetail/ssfsmv7", "label": "Scan for latest ServSafe requirements"}, {"type": "checklist", "section": "daily_procedures", "items": ["Check and record all refrigerator temperatures", "Check and record all freezer temperatures", "Verify hot holding equipment temperatures (135°F or above)", "Complete employee health screening", "Verify sanitizer concentration levels", "Check handwashing stations are fully stocked", "Review previous shift notes", "Inspect deliveries for proper temperatures"]}, {"type": "checklist", "section": "food_storage", "items": ["Label all food with date received", "Store raw proteins below ready-to-eat foods", "Maintain 6 inches clearance from floor", "Check FIFO rotation daily", "Verify freezer at 0°F or below", "Verify refrigerator at 41°F or below"]}, {"type": "checklist", "section": "cleaning_sanitization", "items": ["Test sanitizer concentration every 2 hours", "Clean and sanitize food contact surfaces every 4 hours", "Complete deep cleaning schedule tasks", "Document chemical inventory", "Verify dishwasher temperatures"]}, {"type": "checklist", "section": "employee_training", "items": ["Verify all staff have valid food handler permits", "Complete monthly safety training", "Document allergen awareness training", "Review hand washing procedures quarterly"]}], "file_metadata": {"saved_at": "2025-05-29T07:26:39.761519", "file_path": "./outputs/templates/restaurant_20250529_072639.json", "file_size_bytes": 7581, "generator_version": "2.0"}}