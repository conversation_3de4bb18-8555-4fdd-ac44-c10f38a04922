#!/usr/bin/env python3
"""
Test script for Free LLM providers
This script helps you test and configure free LLM APIs for the SOP Builder
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts', 'utils'))

def test_environment_setup():
    """Test if environment variables are properly configured"""
    print("🔧 Testing Environment Configuration...")
    print("=" * 50)

    # Check for API keys with proper placeholder detection
    providers = {
        'Groq': {
            'key': os.getenv('GROQ_API_KEY', ''),
            'placeholders': ['your_groq_api_key_here', 'gsk_your_actual_groq_api_key_here']
        },
        'Hugging Face': {
            'key': os.getenv('HUGGINGFACE_API_TOKEN', ''),
            'placeholders': ['your_huggingface_token_here', 'hf_your_actual_huggingface_token_here']
        },
        'Together AI': {
            'key': os.getenv('TOGETHER_API_KEY', ''),
            'placeholders': ['your_together_api_key_here', 'your_actual_together_api_key_here']
        },
        'OpenRouter': {
            'key': os.getenv('OPENROUTER_API_KEY', ''),
            'placeholders': ['your_openrouter_api_key_here', 'sk-or-v1-your_actual_key_here']
        }
    }

    configured_count = 0
    for provider, config in providers.items():
        key = config['key']
        placeholders = config['placeholders']

        if key and key not in placeholders and len(key) > 10:
            print(f"✅ {provider}: Configured")
            configured_count += 1
        else:
            print(f"❌ {provider}: Not configured (placeholder or empty)")

    print(f"\n📊 Summary: {configured_count}/3 providers configured")

    if configured_count == 0:
        print("\n⚠️  No providers configured - system will use fallback content")
        print("📖 See FREE_LLM_SETUP_GUIDE.md for setup instructions")
    else:
        print(f"\n✅ {configured_count} provider(s) ready for testing!")

    return configured_count > 0

def test_llm_client():
    """Test the LLM client initialization and provider availability"""
    print("\n🤖 Testing LLM Client...")
    print("=" * 50)

    try:
        from llm_client import FreeLLMClient

        # Initialize client
        client = FreeLLMClient()

        # Get available providers
        available = client.get_available_providers()
        print(f"Available providers: {available}")

        if not available:
            print("❌ No providers available")
            return False

        # Test each provider
        print("\n🧪 Testing provider connections...")
        test_results = client.test_providers()

        for provider, success in test_results.items():
            status = "✅ Working" if success else "❌ Failed"
            print(f"  {provider}: {status}")

        working_providers = [p for p, success in test_results.items() if success]

        if working_providers:
            print(f"\n🎉 {len(working_providers)} provider(s) working correctly!")
            return True
        else:
            print("\n❌ No providers are working")
            return False

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running from the correct directory")
        return False
    except Exception as e:
        print(f"❌ Error testing LLM client: {e}")
        return False

def test_content_generation():
    """Test actual content generation"""
    print("\n📝 Testing Content Generation...")
    print("=" * 50)

    try:
        from llm_client import FreeLLMClient

        client = FreeLLMClient()

        # Simple test prompt
        system_prompt = "You are a helpful assistant."
        user_prompt = "Write a brief 2-sentence introduction to food safety in restaurants."

        print("Generating test content...")
        response = client.generate_content(system_prompt, user_prompt)

        print(f"\n✅ Content generated successfully!")
        print(f"Provider used: {response.provider}")
        print(f"Model: {response.model}")
        print(f"Response time: {response.response_time:.2f}s")
        print(f"Content length: {len(response.content)} characters")

        print(f"\n📄 Generated content:")
        print("-" * 30)
        print(response.content[:200] + "..." if len(response.content) > 200 else response.content)
        print("-" * 30)

        return True

    except Exception as e:
        print(f"❌ Error generating content: {e}")
        return False

def test_sop_generation():
    """Test SOP generation with free LLMs"""
    print("\n🏗️  Testing SOP Generation...")
    print("=" * 50)

    try:
        # Import SOP generator
        sys.path.append(os.path.join(os.path.dirname(__file__), 'scripts', 'generators'))
        from sop_generator import SOPGenerator

        # Create generator
        generator = SOPGenerator('restaurant')

        # Test single section generation
        print("Generating test section...")

        requirements = {
            'compliance': ['FDA Food Code', 'Local Health Department'],
            'industry': 'restaurant'
        }

        content = generator.generate_section('introduction', requirements)

        print(f"✅ Section generated successfully!")
        print(f"Content length: {len(content)} characters")

        print(f"\n📄 Generated section preview:")
        print("-" * 30)
        print(content[:300] + "..." if len(content) > 300 else content)
        print("-" * 30)

        return True

    except Exception as e:
        print(f"❌ Error testing SOP generation: {e}")
        return False

def main():
    """Main test function"""
    print("🆓 Free LLM Provider Test Suite")
    print("=" * 50)
    print("This script tests your free LLM configuration for the SOP Builder")
    print()

    # Test 1: Environment setup
    env_ok = test_environment_setup()

    # Test 2: LLM client
    client_ok = test_llm_client()

    # Test 3: Content generation (only if client works)
    content_ok = False
    if client_ok:
        content_ok = test_content_generation()

    # Test 4: SOP generation
    sop_ok = test_sop_generation()

    # Final summary
    print("\n" + "=" * 50)
    print("🎯 TEST SUMMARY")
    print("=" * 50)

    tests = [
        ("Environment Setup", env_ok),
        ("LLM Client", client_ok),
        ("Content Generation", content_ok),
        ("SOP Generation", sop_ok)
    ]

    for test_name, success in tests:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")

    passed = sum(1 for _, success in tests if success)
    total = len(tests)

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("\n🎉 All tests passed! Your free LLM setup is working perfectly!")
        print("You can now generate SOPs using:")
        print("python scripts/generators/sop_generator.py --type restaurant --verbose")
    elif sop_ok:
        print("\n✅ SOP generation is working (using fallback content)")
        print("Set up free LLM providers for AI-generated content:")
        print("See FREE_LLM_SETUP_GUIDE.md for instructions")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        print("See FREE_LLM_SETUP_GUIDE.md for troubleshooting")

if __name__ == "__main__":
    main()
